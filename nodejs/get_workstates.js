const fs = require('fs');
const axios = require('axios');
require('dotenv').config();

// Get Linear API key from environment variables
const apiKey = process.env.LINEAR_API_KEY;

// Check if API key is available
if (!apiKey) {
  throw new Error("LINEAR_API_KEY not found in .env file. Please make sure the .env file exists and contains the LINEAR_API_KEY variable.");
}

// Linear API endpoint
const url = 'https://api.linear.app/graphql';

// Headers for authentication
const headers = {
  'Authorization': `${apiKey}`,
  'Content-Type': 'application/json',
};

// Function to get workflow states for Infrastructure team
async function getInfrastructureWorkflowStates() {
  // GraphQL query for getting workflow states filtered by Infrastructure team
  const query = `
  query {
    workflowStates(filter: { team: { name: { eq: "Infrastructure" } } }) {
      nodes {
        id
        name
        description
        type
        position
        color
        team {
          id
          name
        }
      }
    }
  }
  `;
  
  // Data to be sent with the request
  const data = {
    query: query
  };
  
  try {
    // Sending the request
    const response = await axios.post(url, data, { headers });
    
    if (response.status === 200 && response.data.data) {
      const workflowStates = response.data.data.workflowStates.nodes;

      let output = `Found ${workflowStates.length} workflow states for Infrastructure team:\n\n`;

      // Group by team for better organization
      const statesByTeam = {};

      workflowStates.forEach(state => {
        const teamName = state.team ? state.team.name : 'No Team';
        const teamId = state.team ? state.team.id : 'no-team';

        if (!statesByTeam[teamName]) {
          statesByTeam[teamName] = {
            teamId: teamId,
            states: []
          };
        }

        statesByTeam[teamName].states.push({
          id: state.id,
          name: state.name,
          description: state.description,
          type: state.type,
          position: state.position,
          color: state.color
        });
      });

      // Build output string organized by team
      Object.keys(statesByTeam).forEach(teamName => {
        const teamData = statesByTeam[teamName];
        output += `\n=== ${teamName} (Team ID: ${teamData.teamId}) ===\n`;

        // Sort states by position
        teamData.states.sort((a, b) => a.position - b.position);

        teamData.states.forEach(state => {
          output += `  ID: ${state.id}\n`;
          output += `  Name: ${state.name}\n`;
          output += `  Type: ${state.type}\n`;
          output += `  Position: ${state.position}\n`;
          output += `  Color: ${state.color}\n`;
          if (state.description) {
            output += `  Description: ${state.description}\n`;
          }
          output += '  ---\n';
        });
      });

      // Also create a simple list of all IDs for Infrastructure team
      output += '\n=== Infrastructure Team Workflow State IDs ===\n';
      workflowStates.forEach(state => {
        output += `${state.id} - ${state.name}\n`;
      });

      // Write to file
      try {
        fs.writeFileSync('workstates.txt', output);
        console.log(`Successfully wrote ${workflowStates.length} workflow states to workstates.txt`);
      } catch (writeError) {
        console.error('Error writing to file:', writeError);
        console.log('Output would have been:');
        console.log(output);
      }
      
    } else {
      console.log('Failed to fetch workflow states:', response.data);
    }
  } catch (error) {
    console.error('Error fetching workflow states:', error.response ? error.response.data : error.message);
  }
}

// Run the function
getInfrastructureWorkflowStates().catch(error => {
  console.error('Error:', error);
});
