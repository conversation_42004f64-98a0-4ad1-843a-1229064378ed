const fs = require('fs');
const yaml = require('js-yaml');
const axios = require('axios');
require('dotenv').config();

// Load YAML file
try {
  const issuesData = yaml.load(fs.readFileSync('issues.yaml', 'utf8'));

  // Get Linear API key from environment variables
  const apiKey = process.env.LINEAR_API_KEY;

  // Check if API key is available
  if (!apiKey) {
    throw new Error("LINEAR_API_KEY not found in .env file. Please make sure the .env file exists and contains the LINEAR_API_KEY variable.");
  }

  // Linear API endpoint for creating issues
  const url = 'https://api.linear.app/graphql';

  // Headers for authentication
  const headers = {
    'Authorization': `${apiKey}`,
    'Content-Type': 'application/json',
  };

  // Function to create an issue in Linear
  async function createIssue(issueData) {
    // GraphQL mutation for creating an issue
    const mutation = `
    mutation($title: String!, $description: String, $teamId: String!, $projectId: String!, $stateId: String) {
      issueCreate(input: {title: $title, description: $description, teamId: $teamId, projectId: $projectId, stateId: $stateId}) {
        success
        issue {
          id
          title
        }
      }
    }
    `;
    
    // Data to be sent with the request
    const data = {
      query: mutation,
      variables: {
        title: issueData.title,
        description: issueData.description || '',
        teamId: issueData.teamId,
        projectId: issueData.projectId,
        stateId: issueData.stateId
      }
    };
    
    try {
      // Sending the request
      const response = await axios.post(url, data, { headers });
      
      if (response.status === 200) {
        console.log(`Issue created successfully:`, response.data);
      } else {
        console.log(`Failed to create issue:`, response.data);
      }
    } catch (error) {
      console.error(`Error creating issue:`, error.response ? error.response.data : error.message);
    }
  }

  // Process all issues
  async function processIssues() {
    for (const issue of issuesData.issues) {
      await createIssue(issue);
    }
  }

  // Run the process
  processIssues().catch(error => {
    console.error('Error processing issues:', error);
  });

} catch (error) {
  console.error('Error:', error.message);
}
