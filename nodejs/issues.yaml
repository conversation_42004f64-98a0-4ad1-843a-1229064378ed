issues:
  - title: "[TASK] API — Deployment Status per Package"
    description: |
      # Background
      E2E tests need to determine **when** to run and **what** to test. Overlapping deployments make it unclear if a given package/version is actually deployed in the target environment.  
      This endpoint will allow querying deployment status per package (primarily for staging) with timestamps to support timeouts and “as-of” snapshots.

      # Scope / Outcome
      - Read-only endpoint reporting whether a package is deployed to an environment.
      - Returns version, commit SHA, start/finish timestamps, and source of truth (ArgoCD/CircleCI).
      - Supports multiple packages in one request.
      - Optional `as_of` timestamp to lock status at E2E test start.

      # API Spec (v1)
      **Path:** `GET /api/v1/deployments/status`  
      **Auth:** Bearer service token.  
      **Query Params:** repo (required), environment (required), package[] (optional), version (optional), pipeline_id (optional), release_id (optional), as_of (optional RFC3339).  
      **Response:** JSON object with per-package status, version, commit SHA, status, source, release_id, pipeline_id, timestamps.  
      **Errors:** 400, 401/403, 404, 429, 5xx.

      # Acceptance Criteria
      - Valid request returns per-package status with timestamps and version.
      - Supports multiple packages per query.
      - `as_of` snapshot produces stable results.
      - 99p latency ≤ 300ms (warm) for ≤ 10 packages.
      - Auth enforced with rotatable service token.
      - Structured logs + metrics implemented.
      - Unit + contract tests with mocked Argo/CircleCI data sources.
      - OpenAPI examples documented.

    teamId: "50e71128-51a1-45f8-963e-9d35b458a205"
    stateId: "e921270a-e2cf-4a27-9f11-e27e7179a144"
    projectId: "23ebd8cf-c5e8-4d9b-a61e-f06c2caca676"
    milestoneId: "cceca42a-387d-4628-b0a0-9c9edfd1c5b6"
